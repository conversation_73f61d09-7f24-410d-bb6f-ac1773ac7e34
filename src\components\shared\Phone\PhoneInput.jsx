import PhoneInputComponent from 'react-phone-number-input'
import 'react-phone-number-input/style.css'
import { TextField } from "@mui/material"
import { forwardRef } from 'react'

// Custom input component for react-phone-number-input to use Material-UI TextField
const CustomInput = forwardRef(({ label, error, helperText, ...props }, ref) => (
    <TextField
        {...props}
        ref={ref}
        label={label}
        variant="standard"
        size="small"
        className="w-full"
        error={error}
        helperText={helperText}
    />
))

CustomInput.displayName = 'CustomInput'

function PhoneInput({
    register = null,
    errors = {},
    onChange,
    label,
    name,
    defaultValue,
    value,
    ...props
}) {
    const isControlled = value !== undefined;

    // Convert defaultValue from local format to international format if needed
    const formatDefaultValue = (val) => {
        if (!val) return undefined;
        // If it's already in international format, return as is
        if (val.startsWith('+')) return val;
        // If it's local format (9 digits), add Azerbaijan country code
        if (val.length === 9 && /^\d+$/.test(val)) {
            return `+994${val}`;
        }
        return val;
    };

    const handleChange = (phoneValue) => {
        // For react-hook-form compatibility
        if (register) {
            const registerProps = register(name);
            if (registerProps.onChange) {
                // Create a synthetic event-like object for react-hook-form
                registerProps.onChange({
                    target: {
                        name,
                        value: phoneValue || ''
                    }
                });
            }
        }

        // Call the provided onChange if it exists
        if (onChange) {
            onChange({
                target: {
                    name,
                    value: phoneValue || ''
                }
            });
        }
    };

    return (
        <PhoneInputComponent
            international
            defaultCountry="AZ"
            value={isControlled ? value : formatDefaultValue(defaultValue)}
            onChange={handleChange}
            inputComponent={CustomInput}
            inputProps={{
                label,
                error: !!errors[name],
                helperText: errors[name]?.message,
                name,
                ...props
            }}
        />
    )
}

export default PhoneInput
