import { TextField } from "@mui/material"

function PhoneInput({
    register = null,
    errors = {},
    // onChange,
    label,
    name,
    defaultValue,
    value,
    ...props
}) {
    const isControlled = value !== undefined;
    const registerProps = register ? register(name) : {};
    
    const handleChange = (e) => {
        // if (onChange) onChange(e);
        if (registerProps.onChange) registerProps.onChange(e);
    };
    
    return (
        <TextField
            {...(register ? registerProps : { name })}
            onChange={handleChange}
            label={label}
            type="tel"
            name={name}
            variant="standard"
            placeholder="501234567"
            error={!!errors[name]}
            helperText={errors[name]?.message}
            size="small"
            {...(isControlled ? { value } : { defaultValue })}
            className="w-full"
            InputProps={{
                startAdornment: <span className="pr-2 text-gray-500 mb-1">+994</span>,
                ...props.InputProps
            }}
            inputProps={{ 
                maxLength: 9,
                ...props.inputProps
            }}
            {...props}
        />
    )
}

export default PhoneInput
