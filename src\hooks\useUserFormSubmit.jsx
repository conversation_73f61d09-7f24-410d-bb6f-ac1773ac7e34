import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { clearSelectedCity } from "@/redux/features/citySlice";
import { clearData } from "@/redux/features/infiniteScrollSlice";
import { objectFormatter } from "@/util/formatters";
import { getToday } from "@/util/dateHelpers";
import { verifyMove } from "@/util/modalHelpers";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";
import { useGetUserProfileQuery } from "@/redux/services/userApiSlice";

export function useUserFormSubmit({
  id,
  user,
  selectedCity,
  addUser,
  updateUser,
  reset,
  getValues,
  trigger,
  isDirty
}) {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { data: userProfile } = useGetUserProfileQuery();

  async function handleSubmit(data, type) {
    data.customer_debt = Number(data["customer_debt"]);
    data.investor_balance = Number(data["investor_balance"]);
    data.date_joined = getToday();
    data.receipt = null;
    data.city_id = selectedCity?.id || null;
    data.phone1 = `+994${data.phone1}`;
    data.phone2 = data.phone2 ? `+994${data.phone2}` : null;

    if (id) {
      data.is_customer = true;
      delete data.investor_balance;
      delete data.date_joined;
    }

    if ((id && user?.investor_balance) || !data.new_balance) delete data.new_balance;

    if (userProfile?.role != "admin" && data.role) {
      delete data.role;
    }

    data = objectFormatter(data);
    if (data.email == null) delete data.email;

    console.log("Submitting Data:", data);

    try {
      console.log(type, isDirty);
      if (type === "save") {
        id ? await updateUser({ data, id }).unwrap() : await addUser(data).unwrap();
        id && showToast("İstifadəçi məlumatları uğurla yeniləndi.", "success");
        reset(getValues(), { keepDirty: false });
        !id && navigate("../users?limit=16&offset=0");
      }
      else if (type === "contract") {
        if (isDirty) {
          const res = await verifyMove();

          if (res == null) return;

          if (res) {
            const isValid = await trigger();
            if (!isValid) return;

            let toBeNavigateID;
            if (id) {
              await updateUser({ data, id }).unwrap();
              toBeNavigateID = id;
            } else {
              const user = await addUser(data).unwrap();
              toBeNavigateID = user.user_id;
            }

            navigate(`/sales/users/${toBeNavigateID}/contract`);
          } else {
            id ? navigate(`/sales/users/${id}/contract`) : navigate(`/sales/users/new/contract`);
          }
        } else {
          id ? navigate(`/sales/users/${id}/contract`) : navigate(`/sales/users/new/contract`);
        }
      }

      !id && dispatch(clearSelectedCity());
      dispatch(clearData());

    } catch (err) {
      console.log(err);
      const msg = errorFinder(err);
      showToast(msg, "error");
    }
  }

  return { handleSubmit };
}
