import { useState, useRef, useEffect } from "react";
import { Helmet } from "react-helmet-async";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { TextField } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import PhoneInput from "@/components/shared/Phone/PhoneInput";
import { administratorFormSchema } from "@/util/schemas/administratorFormSchema";
import { useAddUserMutation, useGetSingleUsersQuery, useUpdateUserMutation } from "@/redux/services/userApiSlice";
import { showToast } from "@/util/showToast";
import { errorFinder } from "@/util/errorHandler";
import { getToday } from "@/util/dateHelpers";
import { useParams } from "react-router-dom";
import { objectFormatter } from "@/util/formatters";

function AdministratorForm() {
  const { id } = useParams();
  const { data: admin, isLoading: getSingleAdminLoading } = useGetSingleUsersQuery(id, { skip: !id });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    clearErrors,
  } = useForm({
    resolver: yupResolver(administratorFormSchema),
    mode: "onBlur",
    reValidateMode: "onSubmit"
  });

  const [imagePreview, setImagePreview] = useState(null);
  const fileInputRef = useRef(null);

  useEffect(() => {
    if (admin?.profile_image) {
      setImagePreview(admin.profile_image);
    }
  }, [admin]);

  const [addUser, { isLoading: addUserLoading }] = useAddUserMutation();
  const [updateUser, { isLoading: updateUserLoading }] = useUpdateUserMutation();

  const onSubmit = async (data) => {
    try {
      let base64Image = null;
      if (data.profileImage instanceof File) {
        base64Image = await new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onerror = () => reject(new Error("FileReader failed"));
          reader.onload = () => resolve(reader.result);
          reader.readAsDataURL(data.profileImage);
        });
      }

      const payload = {
        ...data,
        date_joined: getToday(),
        phone1: `+994${data.phone1}`,
        phone2: data.phone2 ? `+994${data.phone2}` : null,
        receipt: null,
        profileImage: base64Image,
        city: null
      };

      const formattedData = objectFormatter(payload);
      if (formattedData.salary == null) delete formattedData.salary;

      if (id) {
        await updateUser({ data: formattedData, id }).unwrap();
      } else {
        await addUser(formattedData).unwrap();
      }

      showToast("İstifadəçi uğurla əlavə edildi", "success");
    } catch (error) {
      const msg = errorFinder(error);
      showToast(msg, "error");
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files?.[0];
    clearErrors("profileImage");

    if (file) {
      setImagePreview(URL.createObjectURL(file));
      setValue("profileImage", file);
    } else {
      clearImage();
    }
  };

  const clearImage = () => {
    setImagePreview(null);
    setValue("profileImage", "");

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  function handleGlobalChange(e) {
    clearErrors(e.target.name);
  }

  return (
    <>
      <Helmet>
        <title>inKredo | {id ? "Administrator Düzəliş" : "Administrator Əlavə et"}</title>
      </Helmet>
      {getSingleAdminLoading ? (
        <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center">
          <ContentLoadingScreen />
        </div>
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className="max-w-1/3 w-full pt-5">
          <div className="space-y-4">
            {/* Ad Soyad */}
            <div>
              <TextField
                label="Ad Soyad Ata adı"
                variant="standard"
                defaultValue={admin?.fullname || ""}
                {...register("fullname")}
                error={!!errors?.fullname}
                helperText={errors?.fullname?.message}
                className="w-full"
                required
                onChange={handleGlobalChange}
              />
            </div>

            {/* Tel nömrəsi 1 */}
            <div className="flex items-center my-[27px] justify-between">
              <label className="text-[14px] font-[500]">Tel nömrəsi 1 *</label>
              <div className="flex flex-col w-1/2">
                <PhoneInput
                  label=""
                  name="phone1"
                  register={register}
                  errors={errors}
                  onChange={() => clearErrors("phone1")}
                  defaultValue={admin?.phone1 || ""}
                />
              </div>
            </div>

            {/* Tel nömrəsi 2 */}
            <div className="flex items-center my-[27px] justify-between">
              <label className="text-[14px] font-[500]">Tel nömrəsi 2</label>
              <div className="flex flex-col w-1/2">
                <PhoneInput
                  label=""
                  name="phone2"
                  register={register}
                  errors={errors}
                  onChange={() => clearErrors("phone2")}
                  defaultValue={admin?.phone2 || ""}
                />
              </div>
            </div>

            {/* Əməkhaqqı */}
            <div className="flex items-center my-[27px] justify-between">
              <label className="text-[14px] font-[500]">Əməkhaqqı</label>
              <div className="flex flex-col w-1/2">
                <TextField
                  type="number"
                  variant="standard"
                  fullWidth
                  defaultValue={admin?.salary || ""}
                  onChange={() => clearErrors("salary")}
                  {...register("salary")}
                  error={!!errors.salary}
                  helperText={errors.salary?.message}
                />
              </div>
            </div>

            {/* Email */}
            <div className="flex items-center my-[27px] justify-between">
              <label className="text-[14px] font-[500]">Email *</label>
              <div className="flex flex-col w-1/2">
                <TextField
                  type="email"
                  variant="standard"
                  fullWidth
                  defaultValue={admin?.email || ""}
                  {...register("email")}
                  placeholder="nümunə@gmail.com"
                  error={!!errors.email}
                  helperText={errors.email?.message}
                  onChange={handleGlobalChange}
                />
              </div>
            </div>

            {/* İstifadəçi şəkli */}
            <div className="flex items-center my-[27px] justify-between gap-4">
              <label className="text-[14px] font-[500]">İstifadəçi şəkli *</label>
              <div className="flex flex-col w-1/2 gap-2">
                {/* Custom Upload Button */}
                <label className="cursor-pointer inline-block border border-black text-sm px-4 py-2 rounded">
                  {fileInputRef.current?.files?.[0] ? (
                    `Seçilmiş: ${fileInputRef.current.files[0].name}`
                  ) : "Şəkil seçin"}
                  <input
                    type="file"
                    accept="image/*"
                    ref={fileInputRef}
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </label>

                {/* Validation error */}
                {errors.profileImage?.message && (
                  <p className="text-sm text-red-500">{errors.profileImage.message}</p>
                )}

                {/* Image Preview */}
                {imagePreview && (
                  <div className="relative w-fit mt-2">
                    <img
                      src={imagePreview}
                      alt="İstifadəçi şəkli"
                      className="w-32 h-32 object-cover rounded border"
                    />
                    <button
                      type="button"
                      onClick={clearImage}
                      className="absolute -top-2 -right-2 bg-white text-red-500 rounded-full hover:text-red-700"
                    >
                      <CloseIcon fontSize="small" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="fixed bottom-8 right-10 flex items-center gap-[12px]">
            <button
              type="button"
              onClick={handleSubmit(onSubmit)}
              disabled={addUserLoading || updateUserLoading}
              className="defButton bgGreen"
            >
              Yadda Saxla
            </button>
          </div>
        </form>
      )}
    </>
  );
}

export default AdministratorForm;