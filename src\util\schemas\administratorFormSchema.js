import * as yup from "yup";
import { emailRegex } from "./profileSchema";
import { isValidPhoneNumber } from 'react-phone-number-input';

export const administratorFormSchema = yup.object().shape({
  fullname: yup.string().required("Ad Soyad Ata adı daxil edin"),
  phone1: yup
    .string()
    .test(
      "valid-phone",
      "Nömrə doğru strukturda deyil. Düzgün nümunə: +994501234567",
      (value) => {
        if (!value) return false;
        try {
          return isValidPhoneNumber(value);
        } catch {
          return false;
        }
      }
    )
    .required("Nömrə daxil edin"),
  phone2: yup
    .string()
    .test(
      "valid-phone",
      "Nömrə doğru strukturda deyil. Düzgün nümunə: +994501234567",
      (value) => {
        if (!value) return true;
        try {
          return isValidPhoneNumber(value);
        } catch {
          return false;
        }
      }
    ),

  email: yup
    .string()
    .test("valid-email", "E-mail düzgün formatda deyil", (value) => {
      return emailRegex.test(value);
    }),

  profileImage: yup.mixed().test("file-required", "Şəkil seçin", (value) => {
    return value instanceof File || (!!value && typeof value === "object");
  }),
});
