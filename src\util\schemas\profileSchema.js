import * as yup from "yup";
import { isValidPhoneNumber } from 'react-phone-number-input';

export const emailRegex =
  /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x21\x23-\x5b\x5d-\x7f]|\\[\x21-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x21-\x5a\x53-\x7f]|\\[\x21-\x7f])+)\])/i;

export const profileSchema = yup.object().shape({
  phone1: yup
    .string()
    .test(
      "valid-phone",
      "Nömrə doğru strukturda deyil. Düzgün nümunə: +994501234567",
      (value) => {
        if (!value) return true;
        try {
          return isValidPhoneNumber(value);
        } catch {
          return false;
        }
      }
    ),

  phone2: yup
    .string()
    .test(
      "valid-phone",
      "Nömrə doğru strukturda deyil. Düzgün nümunə: +994501234567",
      (value) => {
        if (!value) return true;
        try {
          return isValidPhoneNumber(value);
        } catch {
          return false;
        }
      }
    ),

  email: yup
    .string()
    .matches(emailRegex, "E-mail düzgün formatda deyil")
    .required("E-mail tələb olunur"),

  city: yup.string().required("Şəhər daxil edin"),
});
